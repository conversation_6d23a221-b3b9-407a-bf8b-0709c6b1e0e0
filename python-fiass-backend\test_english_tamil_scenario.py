#!/usr/bin/env python3
"""
Test script to specifically verify English query + Tamil index scenario.
This tests the exact issue you reported.
"""

import sys
import os

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_english_query_tamil_index():
    """Test the specific scenario: English query + Tamil index should translate Tamil response to English."""
    print("🔧 TESTING ENGLISH QUERY + TAMIL INDEX SCENARIO")
    print("=" * 60)
    
    try:
        from services.cross_language_processor import cross_language_processor
        
        # Your specific scenario
        query_language = "English"
        data_language = "Tamil"
        target_language = None  # No explicit target specified
        index_name = "tamil"
        
        print(f"📝 Scenario: English query with Tamil index")
        print(f"   Query Language: {query_language}")
        print(f"   Data Language: {data_language}")
        print(f"   Target Language: {target_language}")
        print(f"   Index Name: {index_name}")
        print()
        
        # Test cross-language processor decision
        should_translate = cross_language_processor.should_use_translation_flow(
            query_language, data_language, target_language, index_name
        )
        
        print(f"🔍 Cross-language processor decision: {should_translate}")
        
        if should_translate:
            print("✅ CORRECT: Cross-language processing will be applied")
            print("   Expected behavior: Tamil response will be translated to English")
            print("   Log should show: 'Applying cross-language processing: Query(English) -> Data(Tamil) -> Response(English)'")
        else:
            print("❌ INCORRECT: Cross-language processing will NOT be applied")
            print("   This means Tamil response will NOT be translated to English")
            print("   User will see Tamil response in UI instead of English")
        
        print()
        
        # Test the skip_translation logic simulation
        print("🔧 Testing skip_translation logic:")
        
        # Simulate the main code logic
        selected_language = "English"  # Detected query language
        requested_index_name = "tamil"
        target_language = None
        
        skip_translation = False
        
        # Case 1: Detected Tamil query with Tamil index and no explicit target = Direct Tamil processing
        if selected_language == "Tamil" and requested_index_name == "tamil" and not target_language:
            skip_translation = True
            reason = "Tamil query with Tamil index - direct processing"
        # Case 2: Tamil query with Tamil index and Tamil target = Direct Tamil processing
        elif selected_language == "Tamil" and requested_index_name == "tamil" and target_language and target_language.lower() in ["tamil", "ta"]:
            skip_translation = True
            reason = "Tamil query with Tamil target - direct processing"
        # Case 3: English query with Tamil index and no target = Cross-language processing
        elif selected_language == "English" and requested_index_name == "tamil" and not target_language:
            skip_translation = False
            reason = "English query with Tamil index - cross-language processing needed"
        # Case 4: Any other Tamil index scenario with explicit non-Tamil target = Cross-language processing
        elif requested_index_name == "tamil" and target_language and target_language.lower() not in ["tamil", "ta"]:
            skip_translation = False
            reason = "Tamil index with non-Tamil target - cross-language processing needed"
        else:
            skip_translation = False
            reason = "Standard processing"
        
        print(f"   Selected Language: {selected_language}")
        print(f"   Index Name: {requested_index_name}")
        print(f"   Target Language: {target_language}")
        print(f"   Skip Translation: {skip_translation}")
        print(f"   Reason: {reason}")
        
        if not skip_translation:
            print("✅ CORRECT: Translation will NOT be skipped")
            print("   Cross-language processing will handle Tamil -> English translation")
        else:
            print("❌ INCORRECT: Translation will be skipped")
            print("   User will get Tamil response instead of English")
        
        return should_translate and not skip_translation
        
    except ImportError as e:
        print(f"❌ Required services not available: {e}")
        return False
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        return False

def main():
    """Run the specific test."""
    print("🚀 TESTING ENGLISH QUERY + TAMIL INDEX SCENARIO")
    print("=" * 60)
    
    success = test_english_query_tamil_index()
    
    print("\n" + "=" * 60)
    if success:
        print("🎯 CONCLUSION: English query + Tamil index scenario is working correctly!")
        print("✅ English queries with Tamil index will get English responses")
        print("✅ Tamil responses will be translated to English for the UI")
        print("✅ No more Tamil responses showing in UI for English queries")
    else:
        print("⚠️ CONCLUSION: Issue detected with English query + Tamil index scenario")
        print("❌ English queries might still get Tamil responses in UI")
    
    return success

if __name__ == "__main__":
    main()