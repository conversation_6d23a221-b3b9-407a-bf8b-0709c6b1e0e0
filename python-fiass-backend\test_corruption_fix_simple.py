#!/usr/bin/env python3
"""
Simple test to verify the word repetition and character diversity functions work correctly.
"""

import re
from collections import Counter

def calculate_script_aware_char_diversity(text):
    """
    Calculate character diversity with script-aware normalization for Indic scripts.
    
    Args:
        text: The text to analyze
        
    Returns:
        float: Normalized character diversity ratio
    """
    if len(text.strip()) <= 10:
        return 1.0
    
    text = text.strip()
    
    # Detect script type
    def detect_script_type(text):
        char_counts = {
            'latin': 0, 'devanagari': 0, 'telugu': 0, 'tamil': 0,
            'kannada': 0, 'malayalam': 0, 'bengali': 0, 'gujarati': 0,
            'arabic': 0, 'chinese': 0, 'japanese': 0, 'korean': 0
        }
        
        for char in text:
            code = ord(char)
            if (0x0041 <= code <= 0x007A) or (0x00C0 <= code <= 0x024F):
                char_counts['latin'] += 1
            elif 0x0900 <= code <= 0x097F:
                char_counts['devanagari'] += 1
            elif 0x0C00 <= code <= 0x0C7F:
                char_counts['telugu'] += 1
            elif 0x0B80 <= code <= 0x0BFF:
                char_counts['tamil'] += 1
            elif 0x0C80 <= code <= 0x0CFF:
                char_counts['kannada'] += 1
            elif 0x0D00 <= code <= 0x0D7F:
                char_counts['malayalam'] += 1
            elif 0x0980 <= code <= 0x09FF:
                char_counts['bengali'] += 1
            elif 0x0A80 <= code <= 0x0AFF:
                char_counts['gujarati'] += 1
            elif 0x0600 <= code <= 0x06FF:
                char_counts['arabic'] += 1
            elif 0x4E00 <= code <= 0x9FFF:
                char_counts['chinese'] += 1
            elif (0x3040 <= code <= 0x309F) or (0x30A0 <= code <= 0x30FF):
                char_counts['japanese'] += 1
            elif 0xAC00 <= code <= 0xD7AF:
                char_counts['korean'] += 1
        
        max_script = max(char_counts, key=char_counts.get)
        return max_script if char_counts[max_script] > 0 else 'default'
    
    script_type = detect_script_type(text)
    
    # For Indic scripts, normalize character diversity calculation
    if script_type in ['telugu', 'tamil', 'kannada', 'malayalam', 'devanagari', 'bengali', 'gujarati']:
        # Separate base characters from combining marks and modifiers
        base_chars = []
        combining_marks = []
        
        for char in text:
            code = ord(char)
            # Check if it's a combining mark or modifier
            if (0x0300 <= code <= 0x036F or  # General combining marks
                (script_type == 'telugu' and (0x0C3E <= code <= 0x0C4D or code in [0x0C55, 0x0C56])) or
                (script_type == 'tamil' and (0x0BBE <= code <= 0x0BCD or code in [0x0BD7])) or
                (script_type == 'kannada' and (0x0CBE <= code <= 0x0CCD or code in [0x0CD5, 0x0CD6])) or
                (script_type == 'malayalam' and (0x0D3E <= code <= 0x0D4D or code in [0x0D57])) or
                (script_type == 'devanagari' and (0x093E <= code <= 0x094D or code in [0x0951, 0x0952, 0x0953, 0x0954])) or
                (script_type == 'bengali' and (0x09BE <= code <= 0x09CD)) or
                (script_type == 'gujarati' and (0x0ABE <= code <= 0x0ACD))):
                combining_marks.append(char)
            else:
                base_chars.append(char)
        
        # Calculate diversity based on base characters primarily
        if len(base_chars) > 0:
            base_diversity = len(set(base_chars)) / len(base_chars)
            # Add a small bonus for combining mark variety (but don't penalize heavily for repetition)
            if len(combining_marks) > 0:
                mark_diversity = len(set(combining_marks)) / len(combining_marks)
                # Weighted combination: 80% base chars, 20% combining marks
                normalized_diversity = (base_diversity * 0.8) + (mark_diversity * 0.2)
            else:
                normalized_diversity = base_diversity
        else:
            # Fallback to standard calculation if no base chars detected
            normalized_diversity = len(set(text)) / len(text)
        
        print(f"🔍 Indic script ({script_type}) diversity calculation:")
        print(f"   Base characters: {len(base_chars)} (unique: {len(set(base_chars))})")
        print(f"   Combining marks: {len(combining_marks)} (unique: {len(set(combining_marks))})")
        print(f"   Normalized diversity: {normalized_diversity:.3f}")
        
        return normalized_diversity
    else:
        # Standard calculation for non-Indic scripts
        return len(set(text)) / len(text)

def detect_word_repetition(text):
    """
    Detect excessive word repetition patterns that indicate corruption.
    
    Args:
        text: The text to analyze
        
    Returns:
        tuple: (is_corrupted, cleaned_text)
    """
    if len(text.strip()) <= 10:
        return False, text
    
    # Split text into words, preserving punctuation context
    words = re.findall(r'\S+', text)
    
    if len(words) < 3:
        return False, text
    
    # Detect consecutive duplicate words
    cleaned_words = []
    repetition_count = 0
    i = 0
    
    while i < len(words):
        current_word = words[i]
        
        # Check if next word is identical (case-insensitive for better detection)
        if i + 1 < len(words) and current_word.lower() == words[i + 1].lower():
            # Found repetition - skip the duplicate
            repetition_count += 1
            cleaned_words.append(current_word)
            i += 2  # Skip both current and next word
            print(f"🔧 Removed duplicate word: '{current_word}'")
        else:
            cleaned_words.append(current_word)
            i += 1
    
    # Calculate repetition ratio
    repetition_ratio = repetition_count / len(words) if len(words) > 0 else 0
    
    print(f"🔍 Word repetition analysis:")
    print(f"   Total words: {len(words)}")
    print(f"   Repeated words found: {repetition_count}")
    print(f"   Repetition ratio: {repetition_ratio:.3f}")
    
    # If more than 10% of words are repeated, consider it corrupted
    is_corrupted = repetition_ratio > 0.1
    
    if is_corrupted:
        print(f"🚨 Excessive word repetition detected ({repetition_ratio:.1%})")
    
    # Reconstruct cleaned text
    cleaned_text = ' '.join(cleaned_words)
    
    return is_corrupted, cleaned_text

def test_corruption_fix():
    """Test the corruption detection and fixing functionality."""
    
    print("🧪 Testing Word Repetition Detection and Character Diversity Fix")
    print("=" * 70)
    
    # Test case from the user's example
    test_text = """வழங்கப்பட்ட சூழலின், "ரசிபுரம், பொறியியல் பொறியியல் கல்லூரியில் தின கொண்டாட்டம்" பற்றி எந்த. இந்த உரை முதன்மையாக பெப்சி ஏற்பாடு செய்யப்பட்ட செய்யப்பட்ட (காவ்வெரிபட்டினம்) இல் ஒரு கிரிக்கெட் போட்டியைப் பற்றி, இதில் இதில், பரிசுகள் மற்றும் பற்றிய.
ரசிபுரத்தில் உள்ள ஒரு கல்லூரியில் பூமி தின கொண்டாட்டங்கள் பற்றிய தகவல்களை தகவல்களை, நீங்கள் நீங்கள் அணுக வேண்டும் அல்லது கூடுதல். தற்போதைய மூலமானது இந்த."""
    
    print("📝 Original Text:")
    print(test_text)
    print()
    
    # Test word repetition detection
    print("🔍 Testing Word Repetition Detection:")
    is_word_corrupted, cleaned_text = detect_word_repetition(test_text)
    
    print()
    print("📝 Cleaned Text:")
    print(cleaned_text)
    print()
    
    # Test script-aware character diversity
    print("🔍 Testing Script-Aware Character Diversity:")
    original_diversity = calculate_script_aware_char_diversity(test_text)
    cleaned_diversity = calculate_script_aware_char_diversity(cleaned_text)
    
    print(f"Original text diversity: {original_diversity:.3f}")
    print(f"Cleaned text diversity: {cleaned_diversity:.3f}")
    print()
    
    # Compare with old method
    print("🔍 Comparison with Old Method:")
    old_diversity = len(set(test_text.strip())) / len(test_text.strip())
    old_cleaned_diversity = len(set(cleaned_text.strip())) / len(cleaned_text.strip())
    
    print(f"Old method - Original: {old_diversity:.3f}")
    print(f"Old method - Cleaned: {old_cleaned_diversity:.3f}")
    print()
    
    # Test with normal Tamil text (no repetition)
    print("🧪 Testing Normal Tamil Text (No Repetition):")
    print("=" * 70)
    
    normal_text = "தமிழ் மொழி மிகவும் பழமையான மொழியாகும். இது திராவிட மொழிக் குடும்பத்தைச் சேர்ந்தது."
    
    print("📝 Normal Text:")
    print(normal_text)
    print()
    
    is_normal_word_corrupted, normal_cleaned = detect_word_repetition(normal_text)
    normal_diversity = calculate_script_aware_char_diversity(normal_text)
    normal_old_diversity = len(set(normal_text.strip())) / len(normal_text.strip())
    
    print(f"Word repetition detected: {is_normal_word_corrupted}")
    print(f"Script-aware diversity: {normal_diversity:.3f}")
    print(f"Old method diversity: {normal_old_diversity:.3f}")
    print()
    
    print("✅ Test completed!")
    print()
    print("📊 Summary:")
    print(f"   • Word repetition successfully detected and cleaned")
    print(f"   • Script-aware diversity calculation working for Tamil text")
    print(f"   • Improvement: {cleaned_diversity:.3f} vs {old_cleaned_diversity:.3f} (script-aware vs old)")

if __name__ == "__main__":
    test_corruption_fix()