#!/usr/bin/env python3
"""
Debug script to check raw Tamil responses before translation.
This will help identify if the corruption is in the data or translation.
"""

import sys
import os

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def analyze_text_for_corruption(text, label="Text"):
    """Analyze text for corruption patterns."""
    print(f"\n🔍 ANALYZING {label.upper()}:")
    print(f"   Content: {text[:100]}{'...' if len(text) > 100 else ''}")
    print(f"   Length: {len(text)}")
    
    if len(text) > 10:
        unique_chars = len(set(text.strip()))
        total_chars = len(text.strip())
        char_diversity = unique_chars / total_chars
        
        print(f"   Unique characters: {unique_chars}")
        print(f"   Character diversity: {char_diversity:.2f}")
        
        if char_diversity < 0.1:
            print("   ❌ WARNING: Low character diversity - possible corruption!")
            from collections import Counter
            char_freq = Counter(text)
            most_common = char_freq.most_common(5)
            print(f"   Most frequent characters: {most_common}")
            return False
        else:
            print("   ✅ Character diversity looks normal")
            return True
    
    return True

def debug_response_processing():
    """Debug the response processing pipeline."""
    print("🔧 DEBUGGING RESPONSE PROCESSING PIPELINE")
    print("=" * 60)
    
    # This is a template for debugging - you'll need to adapt it to your actual search function
    print("📝 To debug your specific issue, please:")
    print("1. Add debug prints in your main query processing code")
    print("2. Check the raw Tamil response before any translation")
    print("3. Verify FAISS search results")
    print()
    
    print("🔍 Add these debug lines to your main code:")
    print("=" * 50)
    
    debug_code = '''
# Add this right after FAISS search, before any translation:
if 'ai_response' in response_data:
    print(f"🔍 RAW TAMIL RESPONSE (before translation): {response_data['ai_response'][:100]}...")
    
    # Check for corruption
    tamil_response = response_data['ai_response']
    if len(tamil_response) > 10:
        unique_chars = len(set(tamil_response.strip()))
        total_chars = len(tamil_response.strip())
        char_diversity = unique_chars / total_chars
        print(f"🔍 Tamil response character diversity: {char_diversity:.2f}")
        
        if char_diversity < 0.1:
            print("❌ CORRUPTION DETECTED in raw Tamil response!")
            from collections import Counter
            char_freq = Counter(tamil_response)
            most_common = char_freq.most_common(5)
            print(f"   Most frequent characters: {most_common}")
        else:
            print("✅ Raw Tamil response looks normal")

# Add this after cross-language processing:
if cross_language_applied and 'ai_response' in response_data:
    print(f"🔍 TRANSLATED RESPONSE: {response_data['ai_response'][:100]}...")
    
    # Check translated response
    translated_response = response_data['ai_response']
    if len(translated_response) > 10:
        unique_chars = len(set(translated_response.strip()))
        total_chars = len(translated_response.strip())
        char_diversity = unique_chars / total_chars
        print(f"🔍 Translated response character diversity: {char_diversity:.2f}")
        
        if char_diversity < 0.1:
            print("❌ CORRUPTION DETECTED in translated response!")
        else:
            print("✅ Translated response looks normal")
'''
    
    print(debug_code)
    print("=" * 50)
    
    return True

def test_sample_corrupted_text():
    """Test with the actual corrupted text you're seeing."""
    print("\n🔧 ANALYZING YOUR CORRUPTED TEXT")
    print("=" * 50)
    
    # The corrupted text you showed
    corrupted_text = "అమిందీ సమ్మేళనం భువనభూషణ్ ఆత్రేయ ఆత్రేయ మాతృభూమి పరచడమంటే ఏమిటి? ఈరొడొడొడొడొడొడొడొడొడొడొడొడొడొడొడొడొడొడొడొడొడొడొడొడొడొడొడొడొడొడొడొడొడొడొడొడొడొడొడొ"
    
    # Split into normal and corrupted parts
    parts = corrupted_text.split("ఈరొడొడొడొడొడొడొడొడొడొడొడొడొడొడొడొడొడొడొడొడొడొడొడొడొడొడొడొడొడొడొడొడొడొడొడొడొడొడొడొ")
    
    if len(parts) >= 2:
        normal_part = parts[0].strip()
        corrupted_part = "ఈరొడొడొడొడొడొడొడొడొడొడొడొడొడొడొడొడొడొడొడొడొడొడొడొడొడొడొడొడొడొడొడొడొడొడొడొడొడొడొడొ"
        
        print("📝 Your response seems to have two parts:")
        analyze_text_for_corruption(normal_part, "Normal Part")
        analyze_text_for_corruption(corrupted_part, "Corrupted Part")
        
        print("\n🔍 ANALYSIS:")
        print("The normal part looks like a proper Telugu translation.")
        print("The corrupted part is clearly repeated characters.")
        print("This suggests the corruption happens during response concatenation or formatting.")
        
    else:
        analyze_text_for_corruption(corrupted_text, "Full Response")

def main():
    """Run debugging analysis."""
    print("🚀 DEBUGGING TAMIL RESPONSE CORRUPTION")
    print("=" * 60)
    
    # Analyze the corrupted text you provided
    test_sample_corrupted_text()
    
    # Provide debugging guidance
    debug_response_processing()
    
    print("\n" + "=" * 60)
    print("🎯 CONCLUSION:")
    print("✅ Translation service is working correctly")
    print("✅ Cross-language processing logic is correct")
    print("❌ Corruption is happening elsewhere in the pipeline")
    print()
    print("🔍 NEXT STEPS:")
    print("1. Add the debug code above to your main query processing")
    print("2. Check if corruption exists in raw Tamil response")
    print("3. Look for response concatenation or formatting issues")
    print("4. Check FAISS search results for corruption")
    
    return True

if __name__ == "__main__":
    main()