#!/usr/bin/env python3
"""
Test script to verify Tamil language processing fixes.
This script tests the improved logic for handling Tamil queries with Tamil index.
"""

import sys
import os

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_tamil_processing_logic():
    """Test the Tamil processing logic with various scenarios."""
    print("🔧 TESTING TAMIL PROCESSING FIXES")
    print("=" * 60)
    
    try:
        from services.cross_language_processor import cross_language_processor
        from services.language_utils import LANGUAGE_CODE_MAP
        
        # Test scenarios
        scenarios = [
            {
                "title": "🟢 Tamil query + Tamil index + No target = Direct processing",
                "query_lang": "Tamil",
                "data_lang": "Tamil", 
                "target_lang": None,
                "index_name": "tamil",
                "expected": False  # Should NOT use translation (direct Tamil processing)
            },
            {
                "title": "🟢 Tamil query + Tamil index + Tamil target = Direct processing",
                "query_lang": "Tamil",
                "data_lang": "Tamil",
                "target_lang": "Tamil", 
                "index_name": "tamil",
                "expected": False  # Should NOT use translation
            },
            {
                "title": "🟢 English query + Tamil index + No target = Cross-language processing",
                "query_lang": "English",
                "data_lang": "Tamil",
                "target_lang": None,
                "index_name": "tamil", 
                "expected": True  # Should use translation (Tamil response -> English)
            },
            {
                "title": "🟢 English query + Tamil index + English target = Cross-language processing",
                "query_lang": "English",
                "data_lang": "Tamil",
                "target_lang": "English",
                "index_name": "tamil",
                "expected": True  # Should use translation
            },
            {
                "title": "🟢 Tamil query + Tamil index + English target = Cross-language processing",
                "query_lang": "Tamil",
                "data_lang": "Tamil",
                "target_lang": "English",
                "index_name": "tamil",
                "expected": True  # Should use translation (explicit English request)
            }
        ]
        
        print("Testing cross-language processor logic:\n")
        
        all_passed = True
        for scenario in scenarios:
            should_translate = cross_language_processor.should_use_translation_flow(
                scenario["query_lang"],
                scenario["data_lang"], 
                scenario["target_lang"],
                scenario["index_name"]
            )
            
            passed = should_translate == scenario["expected"]
            status = "✅ PASS" if passed else "❌ FAIL"
            all_passed = all_passed and passed
            
            print(f"{status} {scenario['title']}")
            print(f"    Query: {scenario['query_lang']}, Data: {scenario['data_lang']}, Target: {scenario['target_lang']}, Index: {scenario['index_name']}")
            print(f"    Expected: {scenario['expected']}, Got: {should_translate}")
            print()
        
        if all_passed:
            print("🎉 ALL TESTS PASSED! Tamil processing logic is working correctly.")
        else:
            print("⚠️ Some tests failed. Please review the logic.")
            
        return all_passed
        
    except ImportError as e:
        print(f"❌ Required services not available: {e}")
        return False
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        return False

def test_language_code_mapping():
    """Test language code mapping for Tamil."""
    print("\n🔤 TESTING LANGUAGE CODE MAPPING")
    print("=" * 40)
    
    try:
        from services.language_utils import LANGUAGE_CODE_MAP
        
        tamil_mappings = [
            ("Tamil", "ta"),
            ("tamil", "ta"), 
            ("ta", "ta")
        ]
        
        for lang_input, expected_code in tamil_mappings:
            actual_code = LANGUAGE_CODE_MAP.get(lang_input, lang_input.lower())
            status = "✅" if actual_code == expected_code else "❌"
            print(f"{status} '{lang_input}' -> '{actual_code}' (expected: '{expected_code}')")
            
    except ImportError as e:
        print(f"❌ Language utils not available: {e}")

def main():
    """Run all tests."""
    print("🚀 STARTING TAMIL PROCESSING FIX TESTS")
    print("=" * 60)
    
    # Test language code mapping
    test_language_code_mapping()
    
    # Test Tamil processing logic
    success = test_tamil_processing_logic()
    
    print("\n" + "=" * 60)
    if success:
        print("🎯 CONCLUSION: Tamil processing fixes are working correctly!")
        print("✅ Tamil queries with Tamil index should now bypass translation")
        print("✅ Cross-language scenarios should still work properly")
    else:
        print("⚠️ CONCLUSION: Some issues detected. Please review the implementation.")
    
    return success

if __name__ == "__main__":
    main()