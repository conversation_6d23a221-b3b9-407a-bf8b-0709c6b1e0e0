#!/usr/bin/env python3
"""
Test script to simulate Telugu query with Tamil index scenario.
This replicates the exact issue you're experiencing.
"""

import sys
import os

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def simulate_telugu_query_tamil_index():
    """Simulate the exact scenario: Telugu query + Tamil index."""
    print("🔧 SIMULATING TELUGU QUERY + TAMIL INDEX SCENARIO")
    print("=" * 60)
    
    try:
        from services.cross_language_processor import cross_language_processor
        
        # Simulate your scenario
        query = "అమిందీ సమ్మేళనం భువనభూషణ్ ఆత్రేయ ఆత్రేయ మాతృభూమి పరచడమంటే ఏమిటి?"  # Telugu query
        query_language = "Telugu"
        data_language = "Tamil"
        user_requested_language = None  # No explicit target, should default to Telugu
        
        print(f"📝 Query: {query}")
        print(f"🔍 Query Language: {query_language}")
        print(f"🔍 Data Language: {data_language}")
        print(f"🔍 Requested Language: {user_requested_language}")
        print()
        
        # Mock search function that returns Tamil response
        def mock_search_function(translated_query, **kwargs):
            print(f"🔍 Mock search called with: {translated_query}")
            # Simulate Tamil response from dataset
            return {
                'ai_response': 'இது ஒரு சோதனை பதில். தமிழ் தரவுத்தளத்திலிருந்து வரும் பதில்.',
                'related_questions': [
                    'இது எப்படி வேலை செய்கிறது?',
                    'மேலும் தகவல் எங்கே கிடைக்கும்?'
                ],
                'confidence': 0.85
            }
        
        # Test cross-language processing
        print("🌐 Starting cross-language processing...")
        result = cross_language_processor.process_cross_language_query(
            query=query,
            query_language=query_language,
            data_language=data_language,
            user_requested_language=user_requested_language,
            search_function=mock_search_function
        )
        
        print("\n🔍 CROSS-LANGUAGE PROCESSING RESULT:")
        print("=" * 50)
        
        if 'error' in result:
            print(f"❌ Error: {result['error']}")
            return False
        
        # Check AI response
        if 'ai_response' in result:
            ai_response = result['ai_response']
            print(f"🤖 AI Response: {ai_response}")
            
            # Check for repeated character issue
            if len(ai_response) > 10:
                unique_chars = len(set(ai_response.strip()))
                total_chars = len(ai_response.strip())
                char_diversity = unique_chars / total_chars
                
                print(f"\n🔍 Response Character Analysis:")
                print(f"   Total characters: {total_chars}")
                print(f"   Unique characters: {unique_chars}")
                print(f"   Character diversity: {char_diversity:.2f}")
                
                if char_diversity < 0.1:
                    print("❌ WARNING: Low character diversity - repeated character issue detected!")
                    print("   This matches the issue you're experiencing")
                    
                    # Show character frequency
                    from collections import Counter
                    char_freq = Counter(ai_response)
                    most_common = char_freq.most_common(5)
                    print(f"   Most frequent characters: {most_common}")
                else:
                    print("✅ Character diversity looks normal")
        
        # Check related questions
        if 'related_questions' in result:
            print(f"\n🔗 Related Questions:")
            for i, question in enumerate(result['related_questions'], 1):
                print(f"   {i}. {question}")
        
        # Check translation metadata
        if 'translation_metadata' in result:
            metadata = result['translation_metadata']
            print(f"\n📊 Translation Metadata:")
            print(f"   Query Translation Applied: {metadata.get('query_translation_applied', False)}")
            print(f"   Response Translation Applied: {metadata.get('response_translation_applied', False)}")
            print(f"   Final Response Language: {metadata.get('final_response_language', 'Unknown')}")
        
        return True
        
    except ImportError as e:
        print(f"❌ Required services not available: {e}")
        return False
    except Exception as e:
        print(f"❌ Simulation failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_direct_tamil_to_telugu():
    """Test direct Tamil to Telugu translation with the exact Tamil text that might be causing issues."""
    print("\n🔧 TESTING DIRECT TAMIL TO TELUGU WITH PROBLEMATIC TEXT")
    print("=" * 60)
    
    try:
        from services.translation_service import TranslationService
        
        translation_service = TranslationService()
        
        # Test with various Tamil texts that might be in your dataset
        test_texts = [
            "இது ஒரு சோதனை பதில். தமிழ் தரவுத்தளத்திலிருந்து வரும் பதில்.",
            "அமிந்தி சம்மேளனம் பற்றிய தகவல்கள்",
            "பூவனபூஷண் ஆத்ரேய பற்றிய விவரங்கள்",
            "மாதृபூமி பத்திரிகை பற்றிய தகவல்கள்"
        ]
        
        for i, tamil_text in enumerate(test_texts, 1):
            print(f"\n📝 Test {i}: {tamil_text}")
            
            result = translation_service.translate_text(tamil_text, 'te', 'ta')
            telugu_text = result['translated_text']
            
            print(f"🔍 Telugu Translation: {telugu_text}")
            
            # Check for issues
            if len(telugu_text) > 10:
                unique_chars = len(set(telugu_text.strip()))
                total_chars = len(telugu_text.strip())
                char_diversity = unique_chars / total_chars
                
                if char_diversity < 0.1:
                    print(f"❌ WARNING: Low character diversity ({char_diversity:.2f}) detected!")
                    from collections import Counter
                    char_freq = Counter(telugu_text)
                    most_common = char_freq.most_common(3)
                    print(f"   Most frequent characters: {most_common}")
                else:
                    print(f"✅ Character diversity normal ({char_diversity:.2f})")
        
        return True
        
    except Exception as e:
        print(f"❌ Direct translation test failed: {e}")
        return False

def main():
    """Run the Telugu query simulation."""
    print("🚀 TELUGU QUERY + TAMIL INDEX DEBUGGING")
    print("=" * 60)
    
    # Simulate the cross-language processing
    simulation_success = simulate_telugu_query_tamil_index()
    
    # Test direct translation
    direct_success = test_direct_tamil_to_telugu()
    
    print("\n" + "=" * 60)
    if simulation_success and direct_success:
        print("🎯 CONCLUSION: Debugging tests completed")
        print("   If character repetition issues were found above, that explains your problem")
        print("   If not, the issue might be in the actual data or response processing")
    else:
        print("⚠️ CONCLUSION: Some debugging tests failed")
        print("   This might help identify the root cause of the repeated character issue")
    
    return simulation_success and direct_success

if __name__ == "__main__":
    main()