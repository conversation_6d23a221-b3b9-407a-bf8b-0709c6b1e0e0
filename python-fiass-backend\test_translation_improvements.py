#!/usr/bin/env python3
"""
Test script for the improved translation service
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from services.translation_service import translation_service

def test_translation_service():
    """Test the improved translation service"""
    print("🧪 Testing Translation Service Improvements")
    print("=" * 50)
    
    # Test cases
    test_cases = [
        {
            'text': 'What Additional Research would be helpful?',
            'source_lang': 'en',
            'target_lang': 'ta',
            'description': 'English to Tamil'
        },
        {
            'text': 'எந்த கூடுதல் ஆராய்ச்சி உதவியாக இருக்கும்?',
            'source_lang': 'ta',
            'target_lang': 'en',
            'description': 'Tamil to English'
        },
        {
            'text': 'Reserve Bank of India cryptocurrency regulation',
            'source_lang': 'en',
            'target_lang': 'te',
            'description': 'English to Telugu'
        },
        {
            'text': 'Mutual funds investment strategies',
            'source_lang': 'en',
            'target_lang': 'kn',
            'description': 'English to Kannada'
        }
    ]
    
    print(f"📊 Initial Health Status:")
    health = translation_service.get_health_status()
    print(f"   Status: {health['status']}")
    print(f"   Message: {health['message']}")
    print()
    
    # Run tests
    for i, test_case in enumerate(test_cases, 1):
        print(f"Test {i}: {test_case['description']}")
        print(f"   Input: {test_case['text'][:50]}...")
        
        try:
            result = translation_service.translate_text(
                text=test_case['text'],
                target_lang=test_case['target_lang'],
                source_lang=test_case['source_lang']
            )
            
            print(f"   Output: {result['translated_text'][:50]}...")
            print(f"   Provider: {result['translation_provider']}")
            print(f"   Cached: {result['cached']}")
            print(f"   ✅ Success")
            
        except Exception as e:
            print(f"   ❌ Error: {e}")
        
        print()
    
    # Show final statistics
    print("📈 Final Statistics:")
    stats = translation_service.get_cache_stats()
    
    print(f"   Cache Info:")
    print(f"     - Total entries: {stats['cache_info']['total_entries']}")
    print(f"     - Valid entries: {stats['cache_info']['valid_entries']}")
    
    print(f"   Translation Stats:")
    print(f"     - Total requests: {stats['translation_stats']['total_requests']}")
    print(f"     - Successful requests: {stats['translation_stats']['successful_requests']}")
    print(f"     - Success rate: {stats['translation_stats']['success_rate_percent']}%")
    
    print(f"   Error Counts:")
    for provider, count in stats['translation_stats']['error_counts'].items():
        print(f"     - {provider}: {count}")
    
    print(f"   Configuration:")
    print(f"     - API timeout: {stats['configuration']['api_timeout']}s")
    print(f"     - Max retries: {stats['configuration']['max_retries']}")
    print(f"     - Translator available: {stats['configuration']['translator_available']}")
    
    print()
    print(f"🏥 Final Health Status:")
    final_health = translation_service.get_health_status()
    print(f"   Status: {final_health['status']}")
    print(f"   Message: {final_health['message']}")
    if final_health['recommendations']:
        print(f"   Recommendations:")
        for rec in final_health['recommendations']:
            print(f"     - {rec}")

def test_language_detection():
    """Test language detection"""
    print("\n🔍 Testing Language Detection")
    print("=" * 30)
    
    test_texts = [
        'Hello, how are you?',
        'வணக்கம், எப்படி இருக்கிறீர்கள்?',
        'నమస్కారం, మీరు ఎలా ఉన్నారు?',
        'ನಮಸ್ಕಾರ, ನೀವು ಹೇಗಿದ್ದೀರಿ?',
        'नमस्ते, आप कैसे हैं?'
    ]
    
    for text in test_texts:
        detected = translation_service.detect_language(text)
        print(f"   '{text[:30]}...' -> {detected}")

if __name__ == "__main__":
    test_language_detection()
    test_translation_service()
    print("\n🎉 Translation service testing completed!")
