"""
Cross-Language Query Processing Service

This service handles translation flows for cases where the user query language
differs from the CSV data language. It translates queries to the data language,
performs search, and translates responses back to the user's requested language.
"""

import logging
from typing import Dict, Any, Optional, Tuple, List
from services.language_utils import enhanced_language_detector, LANGUAGE_CODE_MAP

# Configure logging
logger = logging.getLogger(__name__)

class CrossLanguageProcessor:
    """
    Service to handle cross-language query processing with translation flows.
    """
    
    def __init__(self):
        """Initialize the cross-language processor."""
        self.translation_service = None
        self._initialize_translation_service()
        logger.info("Cross-Language Processor initialized")
    
    def _initialize_translation_service(self):
        """Initialize the translation service."""
        try:
            from services.translation_service import TranslationService
            self.translation_service = TranslationService()
            logger.info("Translation service initialized successfully")
        except ImportError:
            logger.warning("Translation service not available")
            self.translation_service = None
    
    def should_use_translation_flow(self, query_language: str, data_language: str, user_requested_language: Optional[str] = None, index_name: Optional[str] = None) -> bool:
        """
        Determine if translation flow should be used.

        Args:
            query_language: Language of the user query
            data_language: Language of the stored data
            user_requested_language: User's requested response language
            index_name: Name of the index being used (for context-aware decisions)

        Returns:
            bool: True if translation flow should be used
        """
        # Use translation flow if:
        # 1. Query language differs from data language
        # 2. User requested a specific response language different from query language
        # 3. Translation service is available

        if not self.translation_service:
            logger.info("Translation flow not available: Translation service not initialized")
            return False

        # Normalize languages to codes for comparison
        query_lang_code = LANGUAGE_CODE_MAP.get(query_language, query_language.lower() if query_language else 'en')
        data_lang_code = LANGUAGE_CODE_MAP.get(data_language, data_language.lower() if data_language else 'en')

        # Determine final response language
        final_response_language = user_requested_language or query_language
        response_lang_code = LANGUAGE_CODE_MAP.get(final_response_language, final_response_language.lower() if final_response_language else 'en')

        # Special case: Tamil index with Tamil data - handle cross-language scenarios properly
        if index_name == "tamil" and data_lang_code == "ta":
            # Case 1: Tamil query with Tamil data, no specific target = Direct Tamil processing (no translation)
            if query_lang_code == "ta" and (not user_requested_language or response_lang_code == "ta"):
                logger.info(f"Translation flow not needed for Tamil index: Tamil query with Tamil data, keeping Tamil response")
                return False
            
            # Case 2: Non-Tamil query with Tamil data, no specific target = Translate Tamil response to query language
            elif query_lang_code != "ta" and not user_requested_language:
                logger.info(f"Translation flow needed for Tamil index: {query_language} query with Tamil data, will translate response to {query_language}")
                return True
                
            # Case 3: Any query with Tamil data, user explicitly requested different language = Use translation
            elif user_requested_language and response_lang_code != "ta":
                logger.info(f"Translation flow needed for Tamil index: User explicitly requested {user_requested_language} ({response_lang_code}), data in Tamil (ta)")
                return True
                
            # Case 4: Query language differs from data language = Use translation
            elif query_lang_code != data_lang_code:
                logger.info(f"Translation flow needed for Tamil index: Query in {query_language} ({query_lang_code}), data in Tamil (ta)")
                return True
            else:
                logger.info(f"Translation flow not needed for Tamil index: Keeping Tamil response (query: {query_lang_code}, data: {data_lang_code}, target: {response_lang_code})")
                return False

        # Case 1: Query language differs from data language
        if query_lang_code != data_lang_code:
            logger.info(f"Translation flow needed: Query in {query_language} ({query_lang_code}), data in {data_language} ({data_lang_code})")
            return True

        # Case 2: User requested specific response language different from data language
        if user_requested_language and response_lang_code != data_lang_code:
            logger.info(f"Translation flow needed: User requested response in {user_requested_language} ({response_lang_code}), data in {data_language} ({data_lang_code})")
            return True

        logger.info(f"Translation flow not needed: All languages match ({query_lang_code})")
        return False
    
    def process_cross_language_query(self, 
                                   query: str, 
                                   query_language: str, 
                                   data_language: str,
                                   user_requested_language: Optional[str] = None,
                                   search_function: Optional[callable] = None,
                                   **search_kwargs) -> Dict[str, Any]:
        """
        Process a cross-language query with translation flow.
        
        Args:
            query: Original user query
            query_language: Language of the query
            data_language: Language of the stored data
            user_requested_language: User's requested response language
            search_function: Function to perform the actual search
            **search_kwargs: Additional arguments for search function
            
        Returns:
            Dict[str, Any]: Processed response with translations
        """
        if not self.translation_service:
            return {
                'error': 'Translation service not available',
                'error_type': 'translation_service_unavailable'
            }
        
        try:
            # Step 1: Translate query to data language if needed
            translated_query = query
            query_translation_applied = False
            
            if query_language != data_language:
                logger.info(f"Translating query from {query_language} to {data_language}")
                
                query_lang_code = LANGUAGE_CODE_MAP.get(query_language, 'en')
                data_lang_code = LANGUAGE_CODE_MAP.get(data_language, 'en')
                
                translation_result = self.translation_service.translate_text(
                    query, data_lang_code, query_lang_code
                )
                
                if translation_result and translation_result.get('translated_text'):
                    translated_query = translation_result['translated_text']
                    query_translation_applied = True
                    logger.info(f"Query translated: '{query[:50]}...' -> '{translated_query[:50]}...'")
                else:
                    logger.warning("Query translation failed, using original query")
            
            # Step 2: Perform search with translated query
            if search_function:
                search_results = search_function(translated_query, **search_kwargs)
            else:
                search_results = {
                    'error': 'No search function provided',
                    'error_type': 'missing_search_function'
                }
            
            # Step 3: Translate response back to user's requested language if needed
            response_translation_applied = False
            final_response_language = user_requested_language or query_language
            
            if ('error' not in search_results and 
                data_language != final_response_language and 
                'ai_response' in search_results):
                
                logger.info(f"Translating response from {data_language} to {final_response_language}")
                
                data_lang_code = LANGUAGE_CODE_MAP.get(data_language, 'en')
                response_lang_code = LANGUAGE_CODE_MAP.get(final_response_language, 'en')
                
                # Translate AI response
                ai_response_translation = self.translation_service.translate_text(
                    search_results['ai_response'], response_lang_code, data_lang_code
                )
                
                if ai_response_translation and ai_response_translation.get('translated_text'):
                    search_results['ai_response'] = ai_response_translation['translated_text']
                    response_translation_applied = True
                    logger.info("AI response translated successfully")
                
                # Translate related questions if present
                if 'related_questions' in search_results and search_results['related_questions']:
                    translated_questions = []
                    for question in search_results['related_questions']:
                        q_translation = self.translation_service.translate_text(
                            question, response_lang_code, data_lang_code
                        )
                        if q_translation and q_translation.get('translated_text'):
                            translated_questions.append(q_translation['translated_text'])
                        else:
                            translated_questions.append(question)
                    search_results['related_questions'] = translated_questions
                    logger.info(f"Translated {len(translated_questions)} related questions")
            
            # Step 4: Add translation metadata to response
            search_results.update({
                'cross_language_processing': {
                    'original_query': query,
                    'translated_query': translated_query if query_translation_applied else None,
                    'query_language': query_language,
                    'data_language': data_language,
                    'response_language': final_response_language,
                    'query_translation_applied': query_translation_applied,
                    'response_translation_applied': response_translation_applied,
                    'translation_flow_used': True
                }
            })
            
            logger.info("Cross-language processing completed successfully")
            return search_results
            
        except Exception as e:
            logger.error(f"Error in cross-language processing: {str(e)}")
            return {
                'error': f'Cross-language processing failed: {str(e)}',
                'error_type': 'cross_language_processing_error',
                'cross_language_processing': {
                    'original_query': query,
                    'query_language': query_language,
                    'data_language': data_language,
                    'translation_flow_used': True,
                    'error_details': str(e)
                }
            }
    
    def detect_data_language_from_metadata(self, metadata_list: List[Dict[str, Any]]) -> str:
        """
        Detect the language of stored data from metadata.
        
        Args:
            metadata_list: List of metadata dictionaries from search results
            
        Returns:
            str: Detected data language
        """
        if not metadata_list:
            return 'English'
        
        # Collect text samples from metadata
        text_samples = []
        for meta in metadata_list[:10]:  # Sample from first 10 results
            chunk_text = meta.get('chunk_text', '')
            if chunk_text and len(chunk_text.strip()) > 0:
                text_samples.append(chunk_text)
        
        if not text_samples:
            return 'English'
        
        # Combine samples and detect language
        combined_text = ' '.join(text_samples[:5])  # Use first 5 samples
        detected_language, confidence, _ = enhanced_language_detector.detect_language_with_confidence(combined_text)
        
        logger.info(f"Data language detected from metadata: {detected_language} (confidence: {confidence:.3f})")
        return detected_language
    
    def get_translation_service_status(self) -> Dict[str, Any]:
        """
        Get the status of the translation service.
        
        Returns:
            Dict[str, Any]: Translation service status information
        """
        return {
            'translation_service_available': self.translation_service is not None,
            'service_type': type(self.translation_service).__name__ if self.translation_service else None,
            'supported_languages': list(LANGUAGE_CODE_MAP.keys()) if self.translation_service else []
        }


# Global instance for easy access
cross_language_processor = CrossLanguageProcessor()
